from __future__ import annotations

from typing import TYPE_CHECKING, Any

from .base import TelegramObject

if TYPE_CHECKING:
    from .background_type_union import BackgroundTypeUnion


class ChatBackground(TelegramObject):
    """
    This object represents a chat background.

    Source: https://core.telegram.org/bots/api#chatbackground
    """

    type: BackgroundTypeUnion
    """Type of the background"""

    if TYPE_CHECKING:
        # DO NOT EDIT MANUALLY!!!
        # This section was auto-generated via `butcher`

        def __init__(
            __pydantic__self__, *, type: BackgroundTypeUnion, **__pydantic_kwargs: Any
        ) -> None:
            # DO NOT EDIT MANUALLY!!!
            # This method was auto-generated via `butcher`
            # Is needed only for type checking and IDE support without any additional plugins

            super().__init__(type=type, **__pydantic_kwargs)
