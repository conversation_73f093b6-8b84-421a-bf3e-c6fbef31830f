from __future__ import annotations

from typing import Union

from .message_origin_channel import MessageOriginChannel
from .message_origin_chat import Message<PERSON>riginChat
from .message_origin_hidden_user import Message<PERSON>riginHiddenUser
from .message_origin_user import MessageOriginUser

MessageOriginUnion = Union[
    MessageOriginUser, MessageOriginHiddenUser, MessageOriginChat, MessageOriginChannel
]
