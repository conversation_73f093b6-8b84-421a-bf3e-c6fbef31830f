magic_filter-1.0.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
magic_filter-1.0.12.dist-info/METADATA,sha256=lOYu2dS6vE2Ha5UrWEJVNmxokmWbtQHy7w3_v8NxZhk,1536
magic_filter-1.0.12.dist-info/RECORD,,
magic_filter-1.0.12.dist-info/WHEEL,sha256=9QBuHhg6FNW7lppboF2vKVbCGTVzsFykgRQjjlajrhA,87
magic_filter-1.0.12.dist-info/licenses/LICENSE,sha256=mnnw-u5pV8-s5DrMgusp5kyWuD8q_DxyBEyrGWhHhTo,1065
magic_filter/__init__.py,sha256=9JzbtEEmmXCAvOsjWbTJJk5TeelJd6loA3HI6US7mZ0,278
magic_filter/__pycache__/__init__.cpython-310.pyc,,
magic_filter/__pycache__/attrdict.cpython-310.pyc,,
magic_filter/__pycache__/exceptions.cpython-310.pyc,,
magic_filter/__pycache__/helper.cpython-310.pyc,,
magic_filter/__pycache__/magic.cpython-310.pyc,,
magic_filter/__pycache__/util.cpython-310.pyc,,
magic_filter/attrdict.py,sha256=TkQarjPn3cnzcXLAds5vw4T8CZtxe5gFebeMIpOkQxw,368
magic_filter/exceptions.py,sha256=Dn5fIzyQv1Q-IkWaip5CIOAwB8Ye8TM1Xl-5eDEtbFA,361
magic_filter/helper.py,sha256=EyoKzspmOKi740M3_N4SW-Fw8Ha3JWSqiFYdebySLoY,291
magic_filter/magic.py,sha256=HzUY-y9-RA4J0-KDUkJMoegIY5OuwXIM90aRq2T-ffE,11550
magic_filter/operations/__init__.py,sha256=4uGrtmRabLd6jdZrX9xNW8DTjmY4KyWUtKuZgnxOL4U,827
magic_filter/operations/__pycache__/__init__.cpython-310.pyc,,
magic_filter/operations/__pycache__/base.cpython-310.pyc,,
magic_filter/operations/__pycache__/call.cpython-310.pyc,,
magic_filter/operations/__pycache__/cast.cpython-310.pyc,,
magic_filter/operations/__pycache__/combination.cpython-310.pyc,,
magic_filter/operations/__pycache__/comparator.cpython-310.pyc,,
magic_filter/operations/__pycache__/extract.cpython-310.pyc,,
magic_filter/operations/__pycache__/function.cpython-310.pyc,,
magic_filter/operations/__pycache__/getattr.cpython-310.pyc,,
magic_filter/operations/__pycache__/getitem.cpython-310.pyc,,
magic_filter/operations/__pycache__/selector.cpython-310.pyc,,
magic_filter/operations/base.py,sha256=4mOwMvBIcUrBw3ALcW5G6Xk1kYR4pM2-f8-Lr2KpFrQ,231
magic_filter/operations/call.py,sha256=3VcEdjyTO_eNf3Z2aQcMVPZ3Z6JVMl1eeETe_bc4GHQ,527
magic_filter/operations/cast.py,sha256=0J8-WJ233SxAfaFHl7ZyN4xamlJlU7jFWhLaXEQ_5F4,446
magic_filter/operations/combination.py,sha256=y1ID3OaUNcArj_lYFm5cIo5knOSDxY8OMI1uBnR6Muk,1005
magic_filter/operations/comparator.py,sha256=6w3EkrbRKR060p9Az7tvp652jJuR3c-66YT68bJuVtM,522
magic_filter/operations/extract.py,sha256=7Z25YICzvteKMygwt7VHnLeluX2Tx3E8ZKgVUAVZ3kM,613
magic_filter/operations/function.py,sha256=kzIh8SoeIbxN1nqiXJtHhzS8QjBlIcxfNGtKWmYK8is,940
magic_filter/operations/getattr.py,sha256=6GvzUYm8ZQNcDL3h8m82oiUArBzdcXi1976QcbjFUi0,466
magic_filter/operations/getitem.py,sha256=IrHfbDUm6igfvp-tuqB6oAnwJZAbj4_5sXFWaZhKxd4,736
magic_filter/operations/selector.py,sha256=jhXmLg4vrfObetdDLm9laEJz-vsdw8p5_4aZAddzZfw,504
magic_filter/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
magic_filter/util.py,sha256=iz_b9qrLIgrxjSL60xKhJHA69-Suoz5M9yDOSnFc9zk,660
